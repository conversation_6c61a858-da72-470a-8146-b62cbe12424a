import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { useAuth } from "../../hooks/useAuth";
import { useToast } from "../../components/ui/toast";
import {
  Home as HomeIcon,
  Archive,
  Brain,
  Map,
  Trophy,
  LogOut,
  Search,
  Bell,
  User,
  X,
  Menu
} from "lucide-react";

export const Home = (): JSX.Element => {
  const [searchQuery, setSearchQuery] = useState("");
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { user, signOut } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await signOut();
      toast({
        title: "Goodbye!",
        description: "You've been signed out successfully.",
        variant: "success"
      });

      // Navigate back to login page after successful logout
      navigate('/login');
    } catch (error: any) {
      toast({
        title: "Logout Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const sidebarItems = [
    { icon: HomeIcon, label: "home", active: true },
    { icon: Archive, label: "my vault" },
    { icon: Brain, label: "mind games" },
    { icon: Map, label: "visual maps" },
    { icon: Trophy, label: "achievements" },
  ];

  return (
    <div className="min-h-screen w-full bg-[#2E0406] font-sans">
      {/* Top Navigation Bar */}
      <div className="w-full px-8 py-4">
        <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg p-4">
          <div className="flex items-center justify-between">
            {/* Left: Hamburger/Close and Logo */}
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="w-8 h-8 rounded-full hover:bg-[#d4c7b8]/40 transition-all duration-200"
              >
                {sidebarOpen ? (
                  <X className="w-4 h-4 text-[#5a4a3a]/80" />
                ) : (
                  <Menu className="w-4 h-4 text-[#5a4a3a]/80" />
                )}
              </Button>
              <div className="flex items-center gap-2">
                <img
                  src="/app logo.png"
                  alt="Kairo Logo"
                  className="w-8 h-8 rounded-lg object-cover"
                />
              </div>
            </div>

            {/* Center: Search Bar */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#8b7355]/80" />
                <Input
                  placeholder="search"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="bg-[#f0ebe3]/80 backdrop-blur-sm border border-[#d4c7b8]/60 rounded-full h-10 pl-10 pr-4 text-[#2E0406] placeholder:text-[#8b7355]/70 font-poppins text-sm focus:ring-2 focus:ring-[#8b7355]/60 focus:border-[#8b7355]/80 focus:bg-[#f0ebe3]/90 transition-all duration-200"
                />
              </div>
            </div>

            {/* Right: Level, Notifications, User */}
            <div className="flex items-center gap-4">
              {/* Level Progress */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-poppins text-[#2E0406]/90">level 1</span>
                <div className="w-20 h-2 bg-[#d4c7b8]/80 rounded-full overflow-hidden">
                  <div className="w-0 h-full bg-[#8b7355] rounded-full transition-all duration-300" />
                </div>
                <span className="text-xs font-poppins text-[#8b7355]/80">0/20 xp</span>
              </div>

              {/* Notifications */}
              <Button
                variant="ghost"
                size="icon"
                className="w-8 h-8 rounded-full hover:bg-[#d4c7b8]/40 transition-all duration-200"
              >
                <Bell className="w-4 h-4 text-[#5a4a3a]/80" />
              </Button>

              {/* User */}
              <Button
                variant="ghost"
                size="icon"
                className="w-8 h-8 rounded-full hover:bg-[#d4c7b8]/40 transition-all duration-200"
              >
                <User className="w-4 h-4 text-[#5a4a3a]/80" />
              </Button>
              <span className="text-sm font-poppins text-[#2E0406]/90">user</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex px-8 pb-8 gap-6 h-[calc(100vh-120px)]">
        {/* Sidebar */}
        <div className={`transition-all duration-500 ease-in-out ${
          sidebarOpen ? 'w-64 opacity-100' : 'w-0 opacity-0'
        } overflow-hidden`}>
          <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg h-full p-6 w-64">
            {/* Navigation Items */}
            <div className={`space-y-2 mb-8 transition-opacity duration-300 ${
              sidebarOpen ? 'opacity-100 delay-200' : 'opacity-0'
            }`}>
              {sidebarItems.map((item, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  className={`w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm transition-all duration-200 ${
                    item.active
                      ? 'bg-[#2E0406] text-[#faf7f2] hover:bg-[#3d1a1c]'
                      : 'text-[#5a4a3a] hover:bg-[#e0d7cc]/60 hover:text-[#2E0406]'
                  }`}
                >
                  <item.icon className="w-4 h-4" />
                  {item.label}
                </Button>
              ))}
            </div>

            {/* Logout Button */}
            <div className={`absolute bottom-6 left-6 right-6 transition-opacity duration-300 ${
              sidebarOpen ? 'opacity-100 delay-200' : 'opacity-0'
            }`}>
              <Button
                onClick={handleLogout}
                variant="ghost"
                className="w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm text-[#5a4a3a] hover:bg-[#e0d7cc]/60 hover:text-[#2E0406] transition-all duration-200"
              >
                <LogOut className="w-4 h-4" />
                logout
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 transition-all duration-500 ease-in-out">
          <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg h-full p-8">
            {/* Welcome Message */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-cormorant font-bold text-[#2E0406] mb-2">
                welcome back, {user?.displayName || 'user'}!
              </h1>
              <p className="text-[#5a4a3a] font-poppins text-sm">
                ready to explore your sanctuary of thought?
              </p>
            </div>

            {/* Content Area - You can add your main content here */}
            <div className="flex items-center justify-center h-64 border-2 border-dashed border-[#d4c7b8]/70 rounded-xl">
              <p className="text-[#8b7355]/80 font-poppins text-sm">
                Your main content will go here
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

import { useState, useEffect } from 'react';
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  sendPasswordResetEmail,
  User as FirebaseUser,
  onAuthStateChanged
} from 'firebase/auth';
import { auth, googleProvider, facebookProvider } from '../lib/firebase';
import { supabase, User } from '../lib/supabase';

export const useAuth = () => {
  const [user, setUser] = useState<FirebaseUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Test function to verify Supabase connection
  const testSupabaseConnection = async () => {
    try {
      console.log('Testing Supabase connection...');
      const { data, error } = await supabase.from('users').select('count').limit(1);
      if (error) {
        console.error('Supabase connection test failed:', error);
      } else {
        console.log('Supabase connection test successful:', data);
      }
    } catch (err) {
      console.error('Supabase connection test error:', err);
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
      setLoading(false);
    });

    // Test Supabase connection on mount
    testSupabaseConnection();

    return unsubscribe;
  }, []);

  const createUserInSupabase = async (firebaseUser: FirebaseUser, provider: 'email' | 'google' | 'facebook', additionalData?: any) => {
    try {
      if (!firebaseUser?.email) {
        throw new Error('User email is required but not provided by authentication provider');
      }

      console.log('Creating user in Supabase:', {
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        provider,
        displayName: firebaseUser.displayName || additionalData?.displayName
      });

      const userData: Partial<User> = {
        id: firebaseUser.uid,
        auth_provider: provider,
        email: firebaseUser.email,
        password_hash: null, // Firebase handles passwords, so we set this to null
        display_name: firebaseUser.displayName || additionalData?.displayName || '',
        profile_pic_url: firebaseUser.photoURL || '',
        xp: 0,
        level: 1,
        theme: 'dark-crimson',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      if (provider === 'google') {
        userData.google_id = firebaseUser.uid;
      } else if (provider === 'facebook') {
        userData.facebook_id = firebaseUser.uid;
      }

      console.log('User data to insert:', userData);

      const { data, error } = await supabase
        .from('users')
        .upsert(userData, {
          onConflict: 'id'
        })
        .select();

      if (error) {
        console.error('Supabase error details:', error);
        throw error;
      }

      console.log('User successfully created/updated in Supabase:', data);
      return data;
    } catch (error) {
      console.error('Error creating user in Supabase:', error);
      throw error;
    }
  };

  const logLoginHistory = async (userId: string, provider: string) => {
    try {
      const loginData = {
        user_id: userId,
        ip_address: null, // You can implement IP detection if needed
        user_agent: navigator.userAgent,
        provider_used: provider,
        login_time: new Date().toISOString()
      };

      const { error } = await supabase
        .from('login_history')
        .insert(loginData);

      if (error) {
        console.warn('Failed to log login history:', error);
      } else {
        console.log('Login history recorded successfully');
      }
    } catch (error) {
      console.warn('Error logging login history:', error);
    }
  };

  const signInWithEmail = async (email: string, password: string) => {
    try {
      setError(null);
      setLoading(true);
      const result = await signInWithEmailAndPassword(auth, email, password);

      // Log login history
      await logLoginHistory(result.user.uid, 'email');

      return result;
    } catch (error: any) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUpWithEmail = async (email: string, password: string, displayName: string) => {
    try {
      setError(null);
      setLoading(true);
      const result = await createUserWithEmailAndPassword(auth, email, password);

      console.log('Firebase user created successfully:', result.user.uid);

      // Create user in Supabase
      try {
        await createUserInSupabase(result.user, 'email', { displayName });
        console.log('User successfully created in both Firebase and Supabase');
      } catch (supabaseError) {
        console.error('Failed to create user in Supabase, but Firebase user exists:', supabaseError);
        // Don't throw here - Firebase user was created successfully
        // You might want to handle this differently based on your needs
      }

      return result;
    } catch (error: any) {
      console.error('Registration error:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signInWithGoogle = async () => {
    try {
      setError(null);
      setLoading(true);
      const result = await signInWithPopup(auth, googleProvider);

      // Create or update user in Supabase
      await createUserInSupabase(result.user, 'google');

      // Log login history
      await logLoginHistory(result.user.uid, 'google');

      return result;
    } catch (error: any) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signInWithFacebook = async () => {
    try {
      setError(null);
      setLoading(true);
      const result = await signInWithPopup(auth, facebookProvider);

      // Create or update user in Supabase
      await createUserInSupabase(result.user, 'facebook');

      // Log login history
      await logLoginHistory(result.user.uid, 'facebook');

      return result;
    } catch (error: any) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      setError(null);
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      setError(error.message);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await auth.signOut();
    } catch (error: any) {
      setError(error.message);
      throw error;
    }
  };

  return {
    user,
    loading,
    error,
    signInWithEmail,
    signUpWithEmail,
    signInWithGoogle,
    signInWithFacebook,
    resetPassword,
    signOut,
    testSupabaseConnection
  };
};